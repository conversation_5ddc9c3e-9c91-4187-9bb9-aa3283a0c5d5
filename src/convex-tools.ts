import { z } from "zod";
import { ConvexHttpClient } from "convex/browser";
import { api } from "./convex/_generated/api.js";
import { tool } from 'ai';

// Type definitions for better TypeScript safety
interface Contact {
  _id: string;
  name: string;
  email?: string;
  phone?: string;
  jobTitle?: string;
  company?: string;
  location?: string;
  linkedin?: string;
  notes?: string;
  _creationTime: number;
}

interface ConvexToolsEnv {
  PUBLIC_CONVEX_URL?: string;
}

// This factory function injects the user's auth token into the tools
export const convexTools = (convexToken: string, env: ConvexToolsEnv) => {
  console.log("[CONVEX-TOOLS] Initializing convex tools factory");
  console.log("[CONVEX-TOOLS] Auth token provided:", !!convexToken);

  try {
    // Create a new Convex client instance for each request
    const convexUrl = env?.PUBLIC_CONVEX_URL;
    console.log("[CONVEX-TOOLS] Convex URL:", convexUrl);

    if (!convexUrl) {
      const error = new Error(
        "PUBLIC_CONVEX_URL environment variable is not set",
      );
      console.error(
        "[CONVEX-TOOLS] ERROR: Missing PUBLIC_CONVEX_URL environment variable",
      );
      throw error;
    }

    console.log(
      "[CONVEX-TOOLS] Creating ConvexHttpClient with URL:",
      convexUrl,
    );
    const convex = new ConvexHttpClient(convexUrl);

    // Set the auth token for all subsequent requests
    console.log("[CONVEX-TOOLS] Setting auth token for Convex client");
    convex.setAuth(convexToken);
    console.log("[CONVEX-TOOLS] Convex client initialized successfully");

    return {
      // --- READ / SEARCH ---
      listContacts: tool({
        description:
          "Get a list of all of the user's contacts. Use this for general queries like 'show me my contacts'.",
        parameters: z.object({
          limit: z.number().optional().describe("Optional. Maximum number of contacts to retrieve."),
        }),
        execute: async ({ limit }) => {
          console.log("[CONVEX-TOOLS] listContacts: Starting execution");
          console.log(
            "[CONVEX-TOOLS] listContacts: Parameters - limit:",
            limit,
          );

          try {
            console.log(
              "[CONVEX-TOOLS] listContacts: Calling convex.query for getContacts",
            );
            const contacts = await convex.query(api.contacts.getContacts, {
              limit,
            }) as Contact[];
            
            console.log(
              "[CONVEX-TOOLS] listContacts: Successfully retrieved",
              contacts?.length || 0,
              "contacts",
            );
            return { 
              success: true, 
              contacts, 
              data: contacts 
            };
          } catch (error) {
            console.error(
              "[CONVEX-TOOLS] listContacts: ERROR occurred:",
              error,
            );
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return { 
              success: false, 
              error: errorMessage 
            };
          }
        },
      }),

      getContactById: tool({
        description:
          "Get a single, specific contact by their unique ID. Use this when you already have the ID.",
        parameters: z.object({
          id: z.string().describe("The unique ID of the contact to retrieve."),
        }),
        execute: async ({ id }) => {
          console.log("[CONVEX-TOOLS] getContactById: Starting execution");
          console.log("[CONVEX-TOOLS] getContactById: Parameters - id:", id);

          if (!id || typeof id !== "string") {
            console.error(
              "[CONVEX-TOOLS] getContactById: ERROR - Invalid ID provided:",
              id,
            );
            return { 
              success: false, 
              error: "Invalid contact ID provided." 
            };
          }

          try {
            console.log(
              "[CONVEX-TOOLS] getContactById: Calling convex.query for getContactById",
            );
            const contact = await convex.query(api.contacts.getContactById, {
              id,
            }) as Contact | null;

            if (!contact) {
              console.log(
                "[CONVEX-TOOLS] getContactById: Contact not found for ID:",
                id,
              );
              return { 
                success: false, 
                error: "Contact not found." 
              };
            }

            console.log(
              "[CONVEX-TOOLS] getContactById: Successfully retrieved contact:",
              contact.name || "unnamed",
            );
            return { 
              success: true, 
              contact, 
              data: contact 
            };
          } catch (error) {
            console.error(
              "[CONVEX-TOOLS] getContactById: ERROR occurred:",
              error,
            );
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return { 
              success: false, 
              error: errorMessage 
            };
          }
        },
      }),

      searchContacts: tool({
        description:
          "Search for contacts using a query string. Use this for specific searches like 'find John Doe' or 'contacts at Acme Inc' or 'people I met at the conference'.",
        parameters: z.object({
          query: z.string().describe(
            "The search term. Can be a name, email, company, job title, or content from notes."
          ),
        }),
        execute: async ({ query }) => {
          console.log("[CONVEX-TOOLS] searchContacts: Starting execution");
          console.log(
            "[CONVEX-TOOLS] searchContacts: Parameters - query:",
            query,
          );

          if (
            !query ||
            typeof query !== "string" ||
            query.trim().length === 0
          ) {
            console.error(
              "[CONVEX-TOOLS] searchContacts: ERROR - Invalid or empty query provided:",
              query,
            );
            return { 
              success: false, 
              error: "Search query cannot be empty." 
            };
          }

          try {
            console.log(
              "[CONVEX-TOOLS] searchContacts: Calling convex.query for searchContacts",
            );
            const results = await convex.query(api.contacts.searchContacts, {
              searchQuery: query,
            }) as Contact[];
            
            console.log(
              "[CONVEX-TOOLS] searchContacts: Successfully found",
              results?.length || 0,
              "matching contacts",
            );
            return { 
              success: true, 
              results, 
              data: results 
            };
          } catch (error) {
            console.error(
              "[CONVEX-TOOLS] searchContacts: ERROR occurred:",
              error,
            );
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return { 
              success: false, 
              error: errorMessage 
            };
          }
        },
      }),

      // --- CREATE ---
      createContact: tool({
        description:
          "Create a new contact for the user. The name is required. All other fields are optional.",
        parameters: z.object({
          name: z.string().describe("The full name of the contact."),
          email: z.string().optional().describe("The contact's email address."),
          phone: z.string().optional().describe("The contact's phone number."),
          jobTitle: z.string().optional().describe("The contact's job title."),
          company: z.string().optional().describe("The company the contact works for."),
          location: z.string().optional().describe("The city or country of the contact."),
          linkedin: z.string().optional().describe("A URL to the contact's LinkedIn profile."),
          notes: z.string().optional().describe("Any notes about the contact."),
        }),
        execute: async (params) => {
          console.log("[CONVEX-TOOLS] createContact: Starting execution");
          console.log(
            "[CONVEX-TOOLS] createContact: Parameters:",
            JSON.stringify(params, null, 2),
          );

          if (
            !params.name ||
            typeof params.name !== "string" ||
            params.name.trim().length === 0
          ) {
            console.error(
              "[CONVEX-TOOLS] createContact: ERROR - Invalid or missing name:",
              params.name,
            );
            return {
              success: false,
              error: "Contact name is required and cannot be empty.",
            };
          }

          // Validate email format if provided
          if (
            params.email &&
            !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(params.email)
          ) {
            console.error(
              "[CONVEX-TOOLS] createContact: ERROR - Invalid email format:",
              params.email,
            );
            return { 
              success: false, 
              error: "Invalid email format provided." 
            };
          }

          try {
            console.log(
              "[CONVEX-TOOLS] createContact: Calling convex.mutation for createContact",
            );
            const newContactId = await convex.mutation(
              api.contacts.createContact,
              params,
            ) as string;
            
            console.log(
              "[CONVEX-TOOLS] createContact: Successfully created contact with ID:",
              newContactId,
            );
            return {
              success: true,
              newContactId,
              message: "Contact created successfully.",
              data: { id: newContactId, ...params },
            };
          } catch (error) {
            console.error(
              "[CONVEX-TOOLS] createContact: ERROR occurred:",
              error,
            );
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return { 
              success: false, 
              error: errorMessage 
            };
          }
        },
      }),

      // --- UPDATE ---
      updateContact: tool({
        description:
          "Update one or more fields of an existing contact. You must provide the contact's ID.",
        parameters: z.object({
          id: z.string().describe("The unique ID of the contact to update."),
          name: z.string().optional().describe("The new full name of the contact."),
          email: z.string().optional().describe("The new email address."),
          phone: z.string().optional().describe("The new phone number."),
          jobTitle: z.string().optional().describe("The new job title."),
          company: z.string().optional().describe("The new company."),
          location: z.string().optional().describe("The new location."),
          linkedin: z.string().optional().describe("The new LinkedIn profile URL."),
          notes: z.string().optional().describe("Updated notes for the contact."),
        }),
        execute: async (params) => {
          console.log("[CONVEX-TOOLS] updateContact: Starting execution");
          console.log(
            "[CONVEX-TOOLS] updateContact: Parameters:",
            JSON.stringify(params, null, 2),
          );

          if (!params.id || typeof params.id !== "string") {
            console.error(
              "[CONVEX-TOOLS] updateContact: ERROR - Invalid or missing contact ID:",
              params.id,
            );
            return {
              success: false,
              error: "Contact ID is required for updates.",
            };
          }

          // Check if at least one field to update is provided
          const updateFields = Object.keys(params).filter(
            (key) => key !== "id" && params[key as keyof typeof params] !== undefined,
          );
          if (updateFields.length === 0) {
            console.error(
              "[CONVEX-TOOLS] updateContact: ERROR - No fields provided for update",
            );
            return {
              success: false,
              error: "At least one field must be provided for update.",
            };
          }

          // Validate email format if provided
          if (
            params.email &&
            !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(params.email)
          ) {
            console.error(
              "[CONVEX-TOOLS] updateContact: ERROR - Invalid email format:",
              params.email,
            );
            return { 
              success: false, 
              error: "Invalid email format provided." 
            };
          }

          console.log(
            "[CONVEX-TOOLS] updateContact: Updating fields:",
            updateFields,
          );

          try {
            console.log(
              "[CONVEX-TOOLS] updateContact: Calling convex.mutation for updateContact",
            );
            await convex.mutation(api.contacts.updateContact, params);
            console.log(
              "[CONVEX-TOOLS] updateContact: Successfully updated contact with ID:",
              params.id,
            );
            return { 
              success: true, 
              message: "Contact updated successfully.",
              data: params,
            };
          } catch (error) {
            console.error(
              "[CONVEX-TOOLS] updateContact: ERROR occurred:",
              error,
            );
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return { 
              success: false, 
              error: errorMessage 
            };
          }
        },
      }),

      // --- DELETE ---
      deleteContact: tool({
        description: "Delete one or more contacts using their unique IDs.",
        parameters: z.object({
          contactIds: z.array(z.string()).describe("An array of contact IDs to be deleted."),
        }),
        execute: async ({ contactIds }) => {
          console.log("[CONVEX-TOOLS] deleteContact: Starting execution");
          console.log(
            "[CONVEX-TOOLS] deleteContact: Parameters - contactIds:",
            contactIds,
          );

          if (
            !contactIds ||
            !Array.isArray(contactIds) ||
            contactIds.length === 0
          ) {
            console.error(
              "[CONVEX-TOOLS] deleteContact: ERROR - Invalid or empty contactIds array:",
              contactIds,
            );
            return {
              success: false,
              error: "At least one contact ID must be provided for deletion.",
            };
          }

          // Validate all IDs are strings
          const invalidIds = contactIds.filter(
            (id) => !id || typeof id !== "string",
          );
          if (invalidIds.length > 0) {
            console.error(
              "[CONVEX-TOOLS] deleteContact: ERROR - Invalid contact IDs found:",
              invalidIds,
            );
            return {
              success: false,
              error: "All contact IDs must be valid strings.",
            };
          }

          console.log(
            "[CONVEX-TOOLS] deleteContact: Attempting to delete",
            contactIds.length,
            "contact(s)",
          );

          try {
            console.log(
              "[CONVEX-TOOLS] deleteContact: Calling convex.mutation for deleteContact",
            );
            await convex.mutation(api.contacts.deleteContact, { contactIds });
            console.log(
              "[CONVEX-TOOLS] deleteContact: Successfully deleted",
              contactIds.length,
              "contact(s)",
            );
            return {
              success: true,
              message: `Successfully deleted ${contactIds.length} contact(s).`,
              data: { deletedIds: contactIds, count: contactIds.length },
            };
          } catch (error) {
            console.error(
              "[CONVEX-TOOLS] deleteContact: ERROR occurred:",
              error,
            );
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return { 
              success: false, 
              error: errorMessage 
            };
          }
        },
      }),
    };
  } catch (error) {
    console.error(
      "[CONVEX-TOOLS] FATAL ERROR initializing convex tools:",
      error,
    );
    throw error;
  }
};