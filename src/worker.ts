import { authenticateRequest } from "./middleware.js";
import { convexTools } from "./convex-tools.js";
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { generateObject, generateText } from 'ai';
import { z } from 'zod';

// Extend globalThis interface
declare global {
  var PUBLIC_CONVEX_URL: string;
  var CLERK_SECRET_KEY: string;
  var CLERK_PUBLISHABLE_KEY: string;
  var BASE_URL: string;
}

// Allowed HTTP methods for protected routes
const allowedMethods = ["POST", "PUT", "GET", "DELETE", "OPTIONS"];

// CORS headers for all responses
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
  "Access-Control-Max-Age": "86400",
};

// Helper function to create JSON responses with CORS
function jsonResponse(data: any, status = 200): Response {
  try {
    return new Response(JSON.stringify(data), {
      status,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error) {
    console.error(
      "[WORKER] jsonResponse: ERROR creating response:",
      (error as Error).message,
    );
    throw error;
  }
}

// Helper function to handle CORS preflight requests
function handleCORS() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders,
  });
}

// Response schema for structured output
const responseSchema = z.object({
  message: z.string().describe('A clear, descriptive response to the user. This should either ask a clarifying question if more information is needed, or provide a helpful summary of what was accomplished.'),
  success: z.boolean().describe('Whether any requested action was successful. Set to true if the user request was fulfilled or if asking a valid clarifying question. Set to false only if there was an error or failure.'),
  data: z.any().optional().describe('Any data resulting from tool execution (e.g., a list of contacts, search results). Leave undefined if no data to return.'),
});

// Main fetch handler for Cloudflare Workers
export default {
  async fetch(request: Request, env: any, ctx: any): Promise<Response> {
    const url = new URL(request.url);
    const method = request.method;

    try {
      // Handle CORS preflight requests
      if (method === "OPTIONS") {
        return handleCORS();
      }

      // Handle the protected route
      if (url.pathname === "/") {
        return await handleProtectedRoute(request, env);
      }

      // 404 for unmatched routes
      return jsonResponse({ error: "Route not found" }, 404);
    } catch (error) {
      console.error("[WORKER] fetch: ERROR:", (error as Error).message);
      return jsonResponse({ error: "Internal server error" }, 500);
    }
  },
};

// Protected route handler
async function handleProtectedRoute(request: Request, env: any): Promise<Response> {
  try {
    // Only allow specified methods for the protected route
    console.log(
      "[WORKER] handleProtectedRoute: Allowed methods:",
      allowedMethods,
    );

    if (!allowedMethods.includes(request.method)) {
      console.error(
        "[WORKER] handleProtectedRoute: ERROR - Method not allowed:",
        request.method,
      );
      return jsonResponse({ error: "Method not allowed" }, 405);
    }

    console.log(
      "[WORKER] handleProtectedRoute: Method is allowed, proceeding with authentication",
    );

    // Authenticate the request
    console.log("[WORKER] handleProtectedRoute: Calling authenticateRequest");
    const { userId, auth } = await authenticateRequest(request, env);
    console.log(
      "[WORKER] handleProtectedRoute: Authentication successful for userId:",
      userId,
    );

    // Get the JWT token for Convex from Clerk
    console.log(
      "[WORKER] handleProtectedRoute: Getting Convex token from Clerk",
    );
    const convexToken = await auth?.getToken({ template: "convex" });

    if (!convexToken) {
      console.error(
        "[WORKER] handleProtectedRoute: ERROR - Failed to get Convex token",
      );
      return jsonResponse({ error: "Failed to get Convex token" }, 401);
    }

    console.log(
      "[WORKER] handleProtectedRoute: Convex token obtained successfully",
    );

    // Get the request body
    console.log("[WORKER] handleProtectedRoute: Parsing request body");
    let body;
    try {
      body = await request.json();
      console.log(
        "[WORKER] handleProtectedRoute: Request body parsed successfully",
      );
    } catch (error) {
      console.error(
        "[WORKER] handleProtectedRoute: ERROR parsing request body:",
        error,
      );
      return jsonResponse({ error: "Invalid JSON in request body" }, 400);
    }

    const { messages } = body;
    console.log("[WORKER] handleProtectedRoute: Extracted messages from body");

    if (!messages || !Array.isArray(messages)) {
      console.error(
        "[WORKER] handleProtectedRoute: ERROR - Invalid messages format:",
        messages,
      );
      return jsonResponse(
        {
          error: 'Request body must include a "messages" array.',
        },
        400,
      );
    }

    // Set environment variables for global access
    globalThis.PUBLIC_CONVEX_URL = env.PUBLIC_CONVEX_URL;
    globalThis.CLERK_SECRET_KEY = env.CLERK_SECRET_KEY;
    globalThis.CLERK_PUBLISHABLE_KEY = env.CLERK_PUBLISHABLE_KEY;
    globalThis.BASE_URL = env.BASE_URL;

    // Initialize the AI model
    if (!env.GOOGLE_API_KEY) {
      console.error("[WORKER] Missing GOOGLE_API_KEY");
      return jsonResponse({ error: "Google API key is not configured" }, 500);
    }

    // Initialize Google AI provider with explicit API key
    const google = createGoogleGenerativeAI({
      apiKey: env.GOOGLE_API_KEY,
    });
    const model = google('gemini-2.0-flash-lite');

    // Initialize tools
    const tools = convexTools(convexToken, env);

    console.log(
      "[WORKER] AI Request - Messages:",
      messages.length,
      "Tools:",
      Object.keys(tools),
    );

    // Convert messages to the format expected by Vercel AI SDK
    const formattedMessages = messages
      .filter((msg: any) => msg.role === "user" || msg.role === "assistant")
      .map((msg: any) => ({
        role: msg.role as "user" | "assistant",
        content: msg.content,
      }));

    const systemPrompt = `You are a helpful contact management assistant. You MUST either use the available tools to fulfill user requests OR ask specific clarifying questions. Never provide vague responses like "I am retrieving your contacts" without actually using tools.

CRITICAL RULES:
1. If the user asks to see/list/get contacts, you MUST call the listContacts tool
2. If the user wants to add/create/save a contact, you MUST call the createContact tool
3. If the user wants to delete/remove a contact, you MUST call the deleteContact tool  
4. If the user wants to search/find contacts, you MUST call the searchContacts tool
5. If the user wants to update/edit a contact, you MUST call the updateContact tool
6. If the user wants a specific contact by ID, you MUST call the getContactById tool

ONLY ask clarifying questions if you genuinely need more specific information to use a tool (e.g., "delete my contact" without specifying which one).

Examples of CORRECT behavior:
- User: "show me my contacts" → USE listContacts tool → Provide results with actual contact data
- User: "find John" → USE searchContacts tool → Show matching contacts
- User: "delete my contact" → ASK "Which contact would you like to delete? Please provide the name or email."
- User: "add a contact" → ASK "Please provide the contact details you'd like to add (name, email, phone, etc.)."

NEVER respond with vague messages like "I am retrieving..." or "Let me get..." - either use the tools immediately or ask for missing information.

Always provide helpful, descriptive responses that include actual results when tools are used.`;

    try {
      // Generate response with tools
      const result = await generateText({
        model,
        system: systemPrompt,
        messages: formattedMessages,
        tools,
      });

      console.log("[WORKER] AI Response generated successfully");
      
      // Use structured output to format the final response
      const structuredResponse = await generateObject({
        model,
        system: `Based on the conversation and any tool results, provide a structured response to the user. 

If tools were used successfully, include the actual data/results in your message and in the data field.
If you need clarification, ask a specific question.
If there was an error, explain what went wrong.

Always be helpful and descriptive in your message.`,
        messages: [
          ...formattedMessages,
          { role: "assistant", content: result.text },
        ],
        schema: responseSchema,
      });

      console.log("[WORKER] Structured response:", structuredResponse.object);

      return jsonResponse({
        message: structuredResponse.object.message,
        success: structuredResponse.object.success,
        data: structuredResponse.object.data,
      });

    } catch (error) {
      console.error("[WORKER] Error during AI generation:", error);
      
      // Try to extract meaningful error information
      let errorMessage = "I encountered an error while processing your request.";
      let statusCode = 500;
      const errorMsg = (error as Error).message;

      if (errorMsg?.includes("authentication") && errorMsg?.includes("failed")) {
        errorMessage = "Authentication failed. Please try logging in again.";
        statusCode = 401;
      } else if (errorMsg?.includes("rate limit")) {
        errorMessage = "I'm currently experiencing high demand. Please try again in a moment.";
        statusCode = 429;
      } else if (errorMsg?.includes("invalid") && errorMsg?.includes("request")) {
        errorMessage = "There was an issue with your request format. Please try again.";
        statusCode = 400;
      }

      return jsonResponse(
        {
          message: errorMessage,
          success: false,
        },
        statusCode,
      );
    }
  } catch (error) {
    console.error("[WORKER] ERROR:", (error as Error).message);

    let statusCode = 500;
    let message = "An internal error occurred. Please try again.";
    const errorMsg = (error as Error).message;

    if (
      errorMsg === "Authorization header missing" ||
      errorMsg === "Unauthorized"
    ) {
      statusCode = 401;
      message = "Authentication failed. Please log in again.";
    } else if (errorMsg.includes("Invalid JSON")) {
      statusCode = 400;
      message = "Invalid request format. Please check your request and try again.";
    }

    return jsonResponse(
      {
        message: message,
        success: false,
      },
      statusCode,
    );
  }
}