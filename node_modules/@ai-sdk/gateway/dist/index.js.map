{"version": 3, "sources": ["../src/index.ts", "../src/gateway-provider.ts", "../src/errors/as-gateway-error.ts", "../src/errors/create-gateway-error.ts", "../src/errors/gateway-error.ts", "../src/errors/gateway-authentication-error.ts", "../src/errors/gateway-invalid-request-error.ts", "../src/errors/gateway-rate-limit-error.ts", "../src/errors/gateway-model-not-found-error.ts", "../src/errors/gateway-internal-server-error.ts", "../src/errors/gateway-response-error.ts", "../src/errors/extract-api-call-response.ts", "../src/errors/parse-auth-method.ts", "../src/gateway-fetch-metadata.ts", "../src/gateway-language-model.ts", "../src/gateway-embedding-model.ts", "../src/vercel-environment.ts"], "sourcesContent": ["export type { GatewayModelId } from './gateway-language-model-settings';\nexport type {\n  GatewayLanguageModelEntry,\n  GatewayLanguageModelSpecification,\n} from './gateway-model-entry';\nexport {\n  createGatewayProvider,\n  createGatewayProvider as createGateway,\n  gateway,\n} from './gateway-provider';\nexport type {\n  GatewayProvider,\n  GatewayProviderSettings,\n} from './gateway-provider';\nexport type { GatewayProviderOptions } from './gateway-provider-options';\nexport {\n  GatewayError,\n  GatewayAuthenticationError,\n  GatewayInvalidRequestError,\n  GatewayRateLimitError,\n  GatewayModelNotFoundError,\n  GatewayInternalServerError,\n  GatewayResponseError,\n} from './errors';\nexport type { GatewayErrorResponse } from './errors';\n", "import { NoSuchModelError } from '@ai-sdk/provider';\nimport {\n  loadOptionalSetting,\n  withoutTrailingSlash,\n  type FetchFunction,\n} from '@ai-sdk/provider-utils';\nimport { asGatewayError, GatewayAuthenticationError } from './errors';\nimport {\n  GATEWAY_AUTH_METHOD_HEADER,\n  parseAuthMethod,\n} from './errors/parse-auth-method';\nimport {\n  GatewayFetchMetadata,\n  type GatewayFetchMetadataResponse,\n} from './gateway-fetch-metadata';\nimport { GatewayLanguageModel } from './gateway-language-model';\nimport { GatewayEmbeddingModel } from './gateway-embedding-model';\nimport type { GatewayEmbeddingModelId } from './gateway-embedding-model-settings';\nimport { getVercelOidcToken, getVercelRequestId } from './vercel-environment';\nimport type { GatewayModelId } from './gateway-language-model-settings';\nimport type {\n  LanguageModelV2,\n  EmbeddingModelV2,\n  ProviderV2,\n} from '@ai-sdk/provider';\n\nexport interface GatewayProvider extends ProviderV2 {\n  (modelId: GatewayModelId): LanguageModelV2;\n\n  /**\nCreates a model for text generation.\n*/\n  languageModel(modelId: GatewayModelId): LanguageModelV2;\n\n  /**\nReturns available providers and models for use with the remote provider.\n */\n  getAvailableModels(): Promise<GatewayFetchMetadataResponse>;\n\n  /**\nCreates a model for generating text embeddings.\n*/\n  textEmbeddingModel(\n    modelId: GatewayEmbeddingModelId,\n  ): EmbeddingModelV2<string>;\n}\n\nexport interface GatewayProviderSettings {\n  /**\nThe base URL prefix for API calls. Defaults to `https://ai-gateway.vercel.sh/v1/ai`.\n   */\n  baseURL?: string;\n\n  /**\nAPI key that is being sent using the `Authorization` header.\n   */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n\n  /**\nHow frequently to refresh the metadata cache in milliseconds.\n   */\n  metadataCacheRefreshMillis?: number;\n\n  /**\n   * @internal For testing purposes only\n   */\n  _internal?: {\n    currentDate?: () => Date;\n  };\n}\n\nconst AI_GATEWAY_PROTOCOL_VERSION = '0.0.1';\n\n/**\nCreate a remote provider instance.\n */\nexport function createGatewayProvider(\n  options: GatewayProviderSettings = {},\n): GatewayProvider {\n  let pendingMetadata: Promise<GatewayFetchMetadataResponse> | null = null;\n  let metadataCache: GatewayFetchMetadataResponse | null = null;\n  const cacheRefreshMillis =\n    options.metadataCacheRefreshMillis ?? 1000 * 60 * 5;\n  let lastFetchTime = 0;\n\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ??\n    'https://ai-gateway.vercel.sh/v1/ai';\n\n  const getHeaders = async () => {\n    const auth = await getGatewayAuthToken(options);\n    if (auth) {\n      return {\n        Authorization: `Bearer ${auth.token}`,\n        'ai-gateway-protocol-version': AI_GATEWAY_PROTOCOL_VERSION,\n        [GATEWAY_AUTH_METHOD_HEADER]: auth.authMethod,\n        ...options.headers,\n      };\n    }\n\n    throw GatewayAuthenticationError.createContextualError({\n      apiKeyProvided: false,\n      oidcTokenProvided: false,\n      statusCode: 401,\n    });\n  };\n\n  const createO11yHeaders = () => {\n    const deploymentId = loadOptionalSetting({\n      settingValue: undefined,\n      environmentVariableName: 'VERCEL_DEPLOYMENT_ID',\n    });\n    const environment = loadOptionalSetting({\n      settingValue: undefined,\n      environmentVariableName: 'VERCEL_ENV',\n    });\n    const region = loadOptionalSetting({\n      settingValue: undefined,\n      environmentVariableName: 'VERCEL_REGION',\n    });\n\n    return async () => {\n      const requestId = await getVercelRequestId();\n      return {\n        ...(deploymentId && { 'ai-o11y-deployment-id': deploymentId }),\n        ...(environment && { 'ai-o11y-environment': environment }),\n        ...(region && { 'ai-o11y-region': region }),\n        ...(requestId && { 'ai-o11y-request-id': requestId }),\n      };\n    };\n  };\n\n  const createLanguageModel = (modelId: GatewayModelId) => {\n    return new GatewayLanguageModel(modelId, {\n      provider: 'gateway',\n      baseURL,\n      headers: getHeaders,\n      fetch: options.fetch,\n      o11yHeaders: createO11yHeaders(),\n    });\n  };\n\n  const getAvailableModels = async () => {\n    const now = options._internal?.currentDate?.().getTime() ?? Date.now();\n    if (!pendingMetadata || now - lastFetchTime > cacheRefreshMillis) {\n      lastFetchTime = now;\n\n      pendingMetadata = new GatewayFetchMetadata({\n        baseURL,\n        headers: getHeaders,\n        fetch: options.fetch,\n      })\n        .getAvailableModels()\n        .then(metadata => {\n          metadataCache = metadata;\n          return metadata;\n        })\n        .catch(async (error: unknown) => {\n          throw asGatewayError(error, parseAuthMethod(await getHeaders()));\n        });\n    }\n\n    return metadataCache ? Promise.resolve(metadataCache) : pendingMetadata;\n  };\n\n  const provider = function (modelId: GatewayModelId) {\n    if (new.target) {\n      throw new Error(\n        'The Gateway Provider model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createLanguageModel(modelId);\n  };\n\n  provider.getAvailableModels = getAvailableModels;\n  provider.imageModel = (modelId: string) => {\n    throw new NoSuchModelError({ modelId, modelType: 'imageModel' });\n  };\n  provider.languageModel = createLanguageModel;\n  provider.textEmbeddingModel = (modelId: GatewayEmbeddingModelId) => {\n    return new GatewayEmbeddingModel(modelId, {\n      provider: 'gateway',\n      baseURL,\n      headers: getHeaders,\n      fetch: options.fetch,\n      o11yHeaders: createO11yHeaders(),\n    });\n  };\n\n  return provider;\n}\n\nexport const gateway = createGatewayProvider();\n\nexport async function getGatewayAuthToken(\n  options: GatewayProviderSettings,\n): Promise<{\n  token: string;\n  authMethod: 'api-key' | 'oidc';\n} | null> {\n  const apiKey = loadOptionalSetting({\n    settingValue: options.apiKey,\n    environmentVariableName: 'AI_GATEWAY_API_KEY',\n  });\n\n  if (apiKey) {\n    return {\n      token: apiKey,\n      authMethod: 'api-key',\n    };\n  }\n\n  try {\n    const oidcToken = await getVercelOidcToken();\n    return {\n      token: oidcToken,\n      authMethod: 'oidc',\n    };\n  } catch {\n    return null;\n  }\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractApiCallResponse, GatewayError } from '.';\nimport { createGatewayErrorFromResponse } from './create-gateway-error';\n\nexport function asGatewayError(\n  error: unknown,\n  authMethod?: 'api-key' | 'oidc',\n) {\n  if (GatewayError.isInstance(error)) {\n    return error;\n  }\n\n  if (APICallError.isInstance(error)) {\n    return createGatewayErrorFromResponse({\n      response: extractApiCallResponse(error),\n      statusCode: error.statusCode ?? 500,\n      defaultMessage: 'Gateway request failed',\n      cause: error,\n      authMethod,\n    });\n  }\n\n  return createGatewayErrorFromResponse({\n    response: {},\n    statusCode: 500,\n    defaultMessage:\n      error instanceof Error\n        ? `Gateway request failed: ${error.message}`\n        : 'Unknown Gateway error',\n    cause: error,\n    authMethod,\n  });\n}\n", "import { z } from 'zod/v4';\nimport type { GatewayError } from './gateway-error';\nimport { GatewayAuthenticationError } from './gateway-authentication-error';\nimport { GatewayInvalidRequestError } from './gateway-invalid-request-error';\nimport { GatewayRateLimitError } from './gateway-rate-limit-error';\nimport {\n  GatewayModelNotFoundError,\n  modelNotFoundParamSchema,\n} from './gateway-model-not-found-error';\nimport { GatewayInternalServerError } from './gateway-internal-server-error';\nimport { GatewayResponseError } from './gateway-response-error';\n\nexport function createGatewayErrorFromResponse({\n  response,\n  statusCode,\n  defaultMessage = 'Gateway request failed',\n  cause,\n  authMethod,\n}: {\n  response: unknown;\n  statusCode: number;\n  defaultMessage?: string;\n  cause?: unknown;\n  authMethod?: 'api-key' | 'oidc';\n}): GatewayError {\n  const parseResult = gatewayErrorResponseSchema.safeParse(response);\n  if (!parseResult.success) {\n    return new GatewayResponseError({\n      message: `Invalid error response format: ${defaultMessage}`,\n      statusCode,\n      response,\n      validationError: parseResult.error,\n      cause,\n    });\n  }\n\n  const validatedResponse: GatewayErrorResponse = parseResult.data;\n  const errorType = validatedResponse.error.type;\n  const message = validatedResponse.error.message;\n\n  switch (errorType) {\n    case 'authentication_error':\n      return GatewayAuthenticationError.createContextualError({\n        apiKeyProvided: authMethod === 'api-key',\n        oidcTokenProvided: authMethod === 'oidc',\n        statusCode,\n        cause,\n      });\n    case 'invalid_request_error':\n      return new GatewayInvalidRequestError({ message, statusCode, cause });\n    case 'rate_limit_exceeded':\n      return new GatewayRateLimitError({ message, statusCode, cause });\n    case 'model_not_found': {\n      const modelResult = modelNotFoundParamSchema.safeParse(\n        validatedResponse.error.param,\n      );\n      return new GatewayModelNotFoundError({\n        message,\n        statusCode,\n        modelId: modelResult.success ? modelResult.data.modelId : undefined,\n        cause,\n      });\n    }\n    case 'internal_server_error':\n      return new GatewayInternalServerError({ message, statusCode, cause });\n    default:\n      return new GatewayInternalServerError({ message, statusCode, cause });\n  }\n}\n\nconst gatewayErrorResponseSchema = z.object({\n  error: z.object({\n    message: z.string(),\n    type: z.string().nullish(),\n    param: z.unknown().nullish(),\n    code: z.union([z.string(), z.number()]).nullish(),\n  }),\n});\n\nexport type GatewayErrorResponse = z.infer<typeof gatewayErrorResponseSchema>;\n", "const marker = 'vercel.ai.gateway.error';\nconst symbol = Symbol.for(marker);\n\nexport abstract class GatewayError extends Error {\n  private readonly [symbol] = true; // used in isInstance\n\n  abstract readonly name: string;\n  abstract readonly type: string;\n  readonly statusCode: number;\n  readonly cause?: unknown;\n\n  constructor({\n    message,\n    statusCode = 500,\n    cause,\n  }: {\n    message: string;\n    statusCode?: number;\n    cause?: unknown;\n  }) {\n    super(message);\n    this.statusCode = statusCode;\n    this.cause = cause;\n  }\n\n  /**\n   * Checks if the given error is a Gateway Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is a Gateway Error, false otherwise.\n   */\n  static isInstance(error: unknown): error is GatewayError {\n    return GatewayError.hasMarker(error);\n  }\n\n  static hasMarker(error: unknown): error is GatewayError {\n    return (\n      typeof error === 'object' &&\n      error !== null &&\n      symbol in error &&\n      (error as any)[symbol] === true\n    );\n  }\n}\n", "import { GatewayError } from './gateway-error';\n\nconst name = 'GatewayAuthenticationError';\nconst marker = `vercel.ai.gateway.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Authentication failed - invalid API key or OIDC token\n */\nexport class GatewayAuthenticationError extends GatewayError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly name = name;\n  readonly type = 'authentication_error';\n\n  constructor({\n    message = 'Authentication failed',\n    statusCode = 401,\n    cause,\n  }: {\n    message?: string;\n    statusCode?: number;\n    cause?: unknown;\n  } = {}) {\n    super({ message, statusCode, cause });\n  }\n\n  static isInstance(error: unknown): error is GatewayAuthenticationError {\n    return GatewayError.hasMarker(error) && symbol in error;\n  }\n\n  /**\n   * Creates a contextual error message when authentication fails\n   */\n  static createContextualError({\n    apiKeyProvided,\n    oidcTokenProvided,\n    message = 'Authentication failed',\n    statusCode = 401,\n    cause,\n  }: {\n    apiKeyProvided: boolean;\n    oidcTokenProvided: boolean;\n    message?: string;\n    statusCode?: number;\n    cause?: unknown;\n  }): GatewayAuthenticationError {\n    let contextualMessage: string;\n\n    if (apiKeyProvided) {\n      contextualMessage = `AI Gateway authentication failed: Invalid API key provided.\n\nThe token is expected to be provided via the 'apiKey' option or 'AI_GATEWAY_API_KEY' environment variable.`;\n    } else if (oidcTokenProvided) {\n      contextualMessage = `AI Gateway authentication failed: Invalid OIDC token provided.\n\nThe token is expected to be provided via the 'VERCEL_OIDC_TOKEN' environment variable. It expires every 12 hours.\n- make sure your Vercel project settings have OIDC enabled\n- if running locally with 'vercel dev', the token is automatically obtained and refreshed\n- if running locally with your own dev server, run 'vercel env pull' to fetch the token\n- in production/preview, the token is automatically obtained and refreshed\n\nAlternative: Provide an API key via 'apiKey' option or 'AI_GATEWAY_API_KEY' environment variable.`;\n    } else {\n      contextualMessage = `AI Gateway authentication failed: No authentication provided.\n\nProvide either an API key or OIDC token.\n\nAPI key instructions:\n\nThe token is expected to be provided via the 'apiKey' option or 'AI_GATEWAY_API_KEY' environment variable.\n\nOIDC token instructions:\n\nThe token is expected to be provided via the 'VERCEL_OIDC_TOKEN' environment variable. It expires every 12 hours.\n- make sure your Vercel project settings have OIDC enabled\n- if running locally with 'vercel dev', the token is automatically obtained and refreshed\n- if running locally with your own dev server, run 'vercel env pull' to fetch the token\n- in production/preview, the token is automatically obtained and refreshed`;\n    }\n\n    return new GatewayAuthenticationError({\n      message: contextualMessage,\n      statusCode,\n      cause,\n    });\n  }\n}\n", "import { GatewayError } from './gateway-error';\n\nconst name = 'GatewayInvalidRequestError';\nconst marker = `vercel.ai.gateway.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Invalid request - missing headers, malformed data, etc.\n */\nexport class GatewayInvalidRequestError extends GatewayError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly name = name;\n  readonly type = 'invalid_request_error';\n\n  constructor({\n    message = 'Invalid request',\n    statusCode = 400,\n    cause,\n  }: {\n    message?: string;\n    statusCode?: number;\n    cause?: unknown;\n  } = {}) {\n    super({ message, statusCode, cause });\n  }\n\n  static isInstance(error: unknown): error is GatewayInvalidRequestError {\n    return GatewayError.hasMarker(error) && symbol in error;\n  }\n}\n", "import { GatewayError } from './gateway-error';\n\nconst name = 'GatewayRateLimitError';\nconst marker = `vercel.ai.gateway.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Rate limit exceeded.\n */\nexport class GatewayRateLimitError extends GatewayError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly name = name;\n  readonly type = 'rate_limit_exceeded';\n\n  constructor({\n    message = 'Rate limit exceeded',\n    statusCode = 429,\n    cause,\n  }: {\n    message?: string;\n    statusCode?: number;\n    cause?: unknown;\n  } = {}) {\n    super({ message, statusCode, cause });\n  }\n\n  static isInstance(error: unknown): error is GatewayRateLimitError {\n    return GatewayError.hasMarker(error) && symbol in error;\n  }\n}\n", "import { z } from 'zod/v4';\nimport { GatewayError } from './gateway-error';\n\nconst name = 'GatewayModelNotFoundError';\nconst marker = `vercel.ai.gateway.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport const modelNotFoundParamSchema = z.object({\n  modelId: z.string(),\n});\n\n/**\n * Model not found or not available\n */\nexport class GatewayModelNotFoundError extends GatewayError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly name = name;\n  readonly type = 'model_not_found';\n  readonly modelId?: string;\n\n  constructor({\n    message = 'Model not found',\n    statusCode = 404,\n    modelId,\n    cause,\n  }: {\n    message?: string;\n    statusCode?: number;\n    modelId?: string;\n    cause?: unknown;\n  } = {}) {\n    super({ message, statusCode, cause });\n    this.modelId = modelId;\n  }\n\n  static isInstance(error: unknown): error is GatewayModelNotFoundError {\n    return GatewayError.hasMarker(error) && symbol in error;\n  }\n}\n", "import { GatewayError } from './gateway-error';\n\nconst name = 'GatewayInternalServerError';\nconst marker = `vercel.ai.gateway.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Internal server error from the Gateway\n */\nexport class GatewayInternalServerError extends GatewayError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly name = name;\n  readonly type = 'internal_server_error';\n\n  constructor({\n    message = 'Internal server error',\n    statusCode = 500,\n    cause,\n  }: {\n    message?: string;\n    statusCode?: number;\n    cause?: unknown;\n  } = {}) {\n    super({ message, statusCode, cause });\n  }\n\n  static isInstance(error: unknown): error is GatewayInternalServerError {\n    return GatewayError.hasMarker(error) && symbol in error;\n  }\n}\n", "import { GatewayError } from './gateway-error';\nimport type { ZodError } from 'zod/v4';\n\nconst name = 'GatewayResponseError';\nconst marker = `vercel.ai.gateway.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Gateway response parsing error\n */\nexport class GatewayResponseError extends GatewayError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly name = name;\n  readonly type = 'response_error';\n  readonly response?: unknown;\n  readonly validationError?: ZodError;\n\n  constructor({\n    message = 'Invalid response from Gateway',\n    statusCode = 502,\n    response,\n    validationError,\n    cause,\n  }: {\n    message?: string;\n    statusCode?: number;\n    response?: unknown;\n    validationError?: ZodError;\n    cause?: unknown;\n  } = {}) {\n    super({ message, statusCode, cause });\n    this.response = response;\n    this.validationError = validationError;\n  }\n\n  static isInstance(error: unknown): error is GatewayResponseError {\n    return GatewayError.hasMarker(error) && symbol in error;\n  }\n}\n", "import type { APICallError } from '@ai-sdk/provider';\n\nexport function extractApiCallResponse(error: APICallError): unknown {\n  if (error.data !== undefined) {\n    return error.data;\n  }\n  if (error.responseBody != null) {\n    try {\n      return JSON.parse(error.responseBody);\n    } catch {\n      return error.responseBody;\n    }\n  }\n  return {};\n}\n", "import { z } from 'zod/v4';\n\nexport const GATEWAY_AUTH_METHOD_HEADER = 'ai-gateway-auth-method' as const;\n\nexport function parseAuthMethod(headers: Record<string, string | undefined>) {\n  const result = gatewayAuthMethodSchema.safeParse(\n    headers[GATEWAY_AUTH_METHOD_HEADER],\n  );\n  return result.success ? result.data : undefined;\n}\n\nconst gatewayAuthMethodSchema = z.union([\n  z.literal('api-key'),\n  z.literal('oidc'),\n]);\n", "import {\n  createJsonErrorResponseHand<PERSON>,\n  createJsonResponseHandler,\n  getFromApi,\n  resolve,\n} from '@ai-sdk/provider-utils';\nimport { asGatewayError } from './errors';\nimport type { GatewayConfig } from './gateway-config';\nimport type { GatewayLanguageModelEntry } from './gateway-model-entry';\nimport { z } from 'zod/v4';\n\ntype GatewayFetchMetadataConfig = GatewayConfig;\n\nexport interface GatewayFetchMetadataResponse {\n  models: GatewayLanguageModelEntry[];\n}\n\nexport class GatewayFetchMetadata {\n  constructor(private readonly config: GatewayFetchMetadataConfig) {}\n\n  async getAvailableModels(): Promise<GatewayFetchMetadataResponse> {\n    try {\n      const { value } = await getFromApi({\n        url: `${this.config.baseURL}/config`,\n        headers: await resolve(this.config.headers()),\n        successfulResponseHandler: createJsonResponseHandler(\n          gatewayFetchMetadataSchema,\n        ),\n        failedResponseHandler: createJsonErrorResponseHandler({\n          errorSchema: z.any(),\n          errorToMessage: data => data,\n        }),\n        fetch: this.config.fetch,\n      });\n\n      return value;\n    } catch (error) {\n      throw asGatewayError(error);\n    }\n  }\n}\n\nconst gatewayLanguageModelSpecificationSchema = z.object({\n  specificationVersion: z.literal('v2'),\n  provider: z.string(),\n  modelId: z.string(),\n});\n\nconst gatewayLanguageModelPricingSchema = z.object({\n  input: z.string(),\n  output: z.string(),\n});\n\nconst gatewayLanguageModelEntrySchema = z.object({\n  id: z.string(),\n  name: z.string(),\n  description: z.string().nullish(),\n  pricing: gatewayLanguageModelPricingSchema.nullish(),\n  specification: gatewayLanguageModelSpecificationSchema,\n});\n\nconst gatewayFetchMetadataSchema = z.object({\n  models: z.array(gatewayLanguageModelEntrySchema),\n});\n", "import type {\n  LanguageModelV2,\n  LanguageModelV2CallOptions,\n  LanguageModelV2CallWarning,\n  LanguageModelV2FilePart,\n  LanguageModelV2StreamPart,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonErrorResponseHandler,\n  createJsonResponseHandler,\n  postJsonToApi,\n  resolve,\n  type ParseResult,\n  type Resolvable,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod/v4';\nimport type { GatewayConfig } from './gateway-config';\nimport type { GatewayModelId } from './gateway-language-model-settings';\nimport { asGatewayError } from './errors';\nimport { parseAuthMethod } from './errors/parse-auth-method';\n\ntype GatewayChatConfig = GatewayConfig & {\n  provider: string;\n  o11yHeaders: Resolvable<Record<string, string>>;\n};\n\nexport class GatewayLanguageModel implements LanguageModelV2 {\n  readonly specificationVersion = 'v2';\n  readonly supportedUrls = { '*/*': [/.*/] };\n\n  constructor(\n    readonly modelId: GatewayModelId,\n    private readonly config: GatewayChatConfig,\n  ) {}\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private async getArgs(options: Parameters<LanguageModelV2['doGenerate']>[0]) {\n    const { abortSignal: _abortSignal, ...optionsWithoutSignal } = options;\n\n    return {\n      args: this.maybeEncodeFileParts(optionsWithoutSignal),\n      warnings: [],\n    };\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV2['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV2['doGenerate']>>> {\n    const { args, warnings } = await this.getArgs(options);\n    const { abortSignal } = options;\n\n    const resolvedHeaders = await resolve(this.config.headers());\n\n    try {\n      const {\n        responseHeaders,\n        value: responseBody,\n        rawValue: rawResponse,\n      } = await postJsonToApi({\n        url: this.getUrl(),\n        headers: combineHeaders(\n          resolvedHeaders,\n          options.headers,\n          this.getModelConfigHeaders(this.modelId, false),\n          await resolve(this.config.o11yHeaders),\n        ),\n        body: args,\n        successfulResponseHandler: createJsonResponseHandler(z.any()),\n        failedResponseHandler: createJsonErrorResponseHandler({\n          errorSchema: z.any(),\n          errorToMessage: data => data,\n        }),\n        ...(abortSignal && { abortSignal }),\n        fetch: this.config.fetch,\n      });\n\n      return {\n        ...responseBody,\n        request: { body: args },\n        response: { headers: responseHeaders, body: rawResponse },\n        warnings,\n      };\n    } catch (error) {\n      throw asGatewayError(error, parseAuthMethod(resolvedHeaders));\n    }\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV2['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV2['doStream']>>> {\n    const { args, warnings } = await this.getArgs(options);\n    const { abortSignal } = options;\n\n    const resolvedHeaders = await resolve(this.config.headers());\n\n    try {\n      const { value: response, responseHeaders } = await postJsonToApi({\n        url: this.getUrl(),\n        headers: combineHeaders(\n          resolvedHeaders,\n          options.headers,\n          this.getModelConfigHeaders(this.modelId, true),\n          await resolve(this.config.o11yHeaders),\n        ),\n        body: args,\n        successfulResponseHandler: createEventSourceResponseHandler(z.any()),\n        failedResponseHandler: createJsonErrorResponseHandler({\n          errorSchema: z.any(),\n          errorToMessage: data => data,\n        }),\n        ...(abortSignal && { abortSignal }),\n        fetch: this.config.fetch,\n      });\n\n      return {\n        stream: response.pipeThrough(\n          new TransformStream<\n            ParseResult<LanguageModelV2StreamPart>,\n            LanguageModelV2StreamPart\n          >({\n            start(controller) {\n              if (warnings.length > 0) {\n                controller.enqueue({ type: 'stream-start', warnings });\n              }\n            },\n            transform(chunk, controller) {\n              if (chunk.success) {\n                const streamPart = chunk.value;\n\n                // Handle raw chunks: if this is a raw chunk from the gateway API,\n                // only emit it if includeRawChunks is true\n                if (streamPart.type === 'raw' && !options.includeRawChunks) {\n                  return; // Skip raw chunks if not requested\n                }\n\n                if (\n                  streamPart.type === 'response-metadata' &&\n                  streamPart.timestamp &&\n                  typeof streamPart.timestamp === 'string'\n                ) {\n                  streamPart.timestamp = new Date(streamPart.timestamp);\n                }\n\n                controller.enqueue(streamPart);\n              } else {\n                controller.error(\n                  (chunk as { success: false; error: unknown }).error,\n                );\n              }\n            },\n          }),\n        ),\n        request: { body: args },\n        response: { headers: responseHeaders },\n      };\n    } catch (error) {\n      throw asGatewayError(error, parseAuthMethod(resolvedHeaders));\n    }\n  }\n\n  private isFilePart(part: unknown) {\n    return (\n      part && typeof part === 'object' && 'type' in part && part.type === 'file'\n    );\n  }\n\n  /**\n   * Encodes file parts in the prompt to base64. Mutates the passed options\n   * instance directly to avoid copying the file data.\n   * @param options - The options to encode.\n   * @returns The options with the file parts encoded.\n   */\n  private maybeEncodeFileParts(options: LanguageModelV2CallOptions) {\n    for (const message of options.prompt) {\n      for (const part of message.content) {\n        if (this.isFilePart(part)) {\n          const filePart = part as LanguageModelV2FilePart;\n          // If the file part is a URL it will get cleanly converted to a string.\n          // If it's a binary file attachment we convert it to a data url.\n          // In either case, server-side we should only ever see URLs as strings.\n          if (filePart.data instanceof Uint8Array) {\n            const buffer = Uint8Array.from(filePart.data);\n            const base64Data = Buffer.from(buffer).toString('base64');\n            filePart.data = new URL(\n              `data:${filePart.mediaType || 'application/octet-stream'};base64,${base64Data}`,\n            );\n          }\n        }\n      }\n    }\n    return options;\n  }\n\n  private getUrl() {\n    return `${this.config.baseURL}/language-model`;\n  }\n\n  private getModelConfigHeaders(modelId: string, streaming: boolean) {\n    return {\n      'ai-language-model-specification-version': '2',\n      'ai-language-model-id': modelId,\n      'ai-language-model-streaming': String(streaming),\n    };\n  }\n}\n", "import type { EmbeddingModelV2 } from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createJsonResponseHandler,\n  createJsonErrorResponseHand<PERSON>,\n  postJsonToApi,\n  resolve,\n  type Resolvable,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod/v4';\nimport type { GatewayConfig } from './gateway-config';\nimport { asGatewayError } from './errors';\nimport { parseAuthMethod } from './errors/parse-auth-method';\nimport type { SharedV2ProviderMetadata } from '@ai-sdk/provider';\n\nexport class GatewayEmbeddingModel implements EmbeddingModelV2<string> {\n  readonly specificationVersion = 'v2';\n  readonly maxEmbeddingsPerCall = 2048;\n  readonly supportsParallelCalls = true;\n\n  constructor(\n    readonly modelId: string,\n    private readonly config: GatewayConfig & {\n      provider: string;\n      o11yHeaders: Resolvable<Record<string, string>>;\n    },\n  ) {}\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  async doEmbed({\n    values,\n    headers,\n    abortSignal,\n    providerOptions,\n  }: Parameters<EmbeddingModelV2<string>['doEmbed']>[0]): Promise<\n    Awaited<ReturnType<EmbeddingModelV2<string>['doEmbed']>>\n  > {\n    const resolvedHeaders = await resolve(this.config.headers());\n    try {\n      const {\n        responseHeaders,\n        value: responseBody,\n        rawValue,\n      } = await postJsonToApi({\n        url: this.getUrl(),\n        headers: combineHeaders(\n          resolvedHeaders,\n          headers ?? {},\n          this.getModelConfigHeaders(),\n          await resolve(this.config.o11yHeaders),\n        ),\n        body: {\n          input: values.length === 1 ? values[0] : values,\n          ...(providerOptions ?? {}),\n        },\n        successfulResponseHandler: createJsonResponseHandler(\n          gatewayEmbeddingResponseSchema,\n        ),\n        failedResponseHandler: createJsonErrorResponseHandler({\n          errorSchema: z.any(),\n          errorToMessage: data => data,\n        }),\n        ...(abortSignal && { abortSignal }),\n        fetch: this.config.fetch,\n      });\n\n      return {\n        embeddings: responseBody.embeddings,\n        usage: responseBody.usage ?? undefined,\n        providerMetadata:\n          responseBody.providerMetadata as unknown as SharedV2ProviderMetadata,\n        response: { headers: responseHeaders, body: rawValue },\n      };\n    } catch (error) {\n      throw asGatewayError(error, parseAuthMethod(resolvedHeaders));\n    }\n  }\n\n  private getUrl() {\n    return `${this.config.baseURL}/embedding-model`;\n  }\n\n  private getModelConfigHeaders() {\n    return {\n      'ai-embedding-model-specification-version': '2',\n      'ai-model-id': this.modelId,\n    };\n  }\n}\n\nconst gatewayEmbeddingResponseSchema = z.object({\n  embeddings: z.array(z.array(z.number())),\n  usage: z.object({ tokens: z.number() }).nullish(),\n  providerMetadata: z\n    .record(z.string(), z.record(z.string(), z.unknown()))\n    .optional(),\n});\n", "import { GatewayAuthenticationError } from './errors';\n\nexport async function getVercelOidcToken(): Promise<string> {\n  const token =\n    getContext().headers?.['x-vercel-oidc-token'] ??\n    process.env.VERCEL_OIDC_TOKEN;\n\n  if (!token) {\n    throw new GatewayAuthenticationError({\n      message: 'OIDC token not available',\n      statusCode: 401,\n    });\n  }\n\n  return token;\n}\n\nexport async function getVercelRequestId(): Promise<string | undefined> {\n  return getContext().headers?.['x-vercel-id'];\n}\n\ntype Context = {\n  headers?: Record<string, string>;\n};\n\nconst SYMBOL_FOR_REQ_CONTEXT = Symbol.for('@vercel/request-context');\n\nfunction getContext(): Context {\n  const fromSymbol: typeof globalThis & {\n    [SYMBOL_FOR_REQ_CONTEXT]?: { get?: () => Context };\n  } = globalThis;\n  return fromSymbol[SYMBOL_FOR_REQ_CONTEXT]?.get?.() ?? {};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAA,mBAAiC;AACjC,IAAAC,yBAIO;;;ACLP,sBAA6B;;;ACA7B,IAAAC,aAAkB;;;ACAlB,IAAM,SAAS;AACf,IAAM,SAAS,OAAO,IAAI,MAAM;AADhC;AAGO,IAAe,eAAf,MAAe,uBAAqB,YACvB,aADuB,IAAM;AAAA,EAQ/C,YAAY;AAAA,IACV;AAAA,IACA,aAAa;AAAA,IACb;AAAA,EACF,GAIG;AACD,UAAM,OAAO;AAhBf,SAAkB,MAAU;AAiB1B,SAAK,aAAa;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,WAAW,OAAuC;AACvD,WAAO,cAAa,UAAU,KAAK;AAAA,EACrC;AAAA,EAEA,OAAO,UAAU,OAAuC;AACtD,WACE,OAAO,UAAU,YACjB,UAAU,QACV,UAAU,SACT,MAAc,MAAM,MAAM;AAAA,EAE/B;AACF;;;ACxCA,IAAM,OAAO;AACb,IAAMC,UAAS,2BAA2B,IAAI;AAC9C,IAAMC,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE,KAAAC;AASO,IAAM,6BAAN,MAAM,qCAAmCA,MAAA,cAC5BD,MAAAD,SAD4BE,KAAa;AAAA,EAM3D,YAAY;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb;AAAA,EACF,IAII,CAAC,GAAG;AACN,UAAM,EAAE,SAAS,YAAY,MAAM,CAAC;AAdtC,SAAkBD,OAAU;AAE5B;AAAA,SAAS,OAAO;AAChB,SAAS,OAAO;AAAA,EAYhB;AAAA,EAEA,OAAO,WAAW,OAAqD;AACrE,WAAO,aAAa,UAAU,KAAK,KAAKD,WAAU;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,sBAAsB;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,aAAa;AAAA,IACb;AAAA,EACF,GAM+B;AAC7B,QAAI;AAEJ,QAAI,gBAAgB;AAClB,0BAAoB;AAAA;AAAA;AAAA,IAGtB,WAAW,mBAAmB;AAC5B,0BAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAStB,OAAO;AACL,0BAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAetB;AAEA,WAAO,IAAI,4BAA2B;AAAA,MACpC,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;ACrFA,IAAMG,QAAO;AACb,IAAMC,UAAS,2BAA2BD,KAAI;AAC9C,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE,KAAAC;AASO,IAAM,6BAAN,eAAyCA,MAAA,cAC5BD,MAAAD,SAD4BE,KAAa;AAAA,EAM3D,YAAY;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb;AAAA,EACF,IAII,CAAC,GAAG;AACN,UAAM,EAAE,SAAS,YAAY,MAAM,CAAC;AAdtC,SAAkBD,OAAU;AAE5B;AAAA,SAAS,OAAOH;AAChB,SAAS,OAAO;AAAA,EAYhB;AAAA,EAEA,OAAO,WAAW,OAAqD;AACrE,WAAO,aAAa,UAAU,KAAK,KAAKE,WAAU;AAAA,EACpD;AACF;;;AC5BA,IAAMG,QAAO;AACb,IAAMC,UAAS,2BAA2BD,KAAI;AAC9C,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE,KAAAC;AASO,IAAM,wBAAN,eAAoCA,MAAA,cACvBD,MAAAD,SADuBE,KAAa;AAAA,EAMtD,YAAY;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb;AAAA,EACF,IAII,CAAC,GAAG;AACN,UAAM,EAAE,SAAS,YAAY,MAAM,CAAC;AAdtC,SAAkBD,OAAU;AAE5B;AAAA,SAAS,OAAOH;AAChB,SAAS,OAAO;AAAA,EAYhB;AAAA,EAEA,OAAO,WAAW,OAAgD;AAChE,WAAO,aAAa,UAAU,KAAK,KAAKE,WAAU;AAAA,EACpD;AACF;;;AC9BA,gBAAkB;AAGlB,IAAMG,QAAO;AACb,IAAMC,UAAS,2BAA2BD,KAAI;AAC9C,IAAME,UAAS,OAAO,IAAID,OAAM;AAEzB,IAAM,2BAA2B,YAAE,OAAO;AAAA,EAC/C,SAAS,YAAE,OAAO;AACpB,CAAC;AATD,IAAAE,KAAAC;AAcO,IAAM,4BAAN,eAAwCA,MAAA,cAC3BD,MAAAD,SAD2BE,KAAa;AAAA,EAO1D,YAAY;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,IAKI,CAAC,GAAG;AACN,UAAM,EAAE,SAAS,YAAY,MAAM,CAAC;AAjBtC,SAAkBD,OAAU;AAE5B;AAAA,SAAS,OAAOH;AAChB,SAAS,OAAO;AAed,SAAK,UAAU;AAAA,EACjB;AAAA,EAEA,OAAO,WAAW,OAAoD;AACpE,WAAO,aAAa,UAAU,KAAK,KAAKE,WAAU;AAAA,EACpD;AACF;;;ACrCA,IAAMG,QAAO;AACb,IAAMC,UAAS,2BAA2BD,KAAI;AAC9C,IAAME,UAAS,OAAO,IAAID,OAAM;AAJhC,IAAAE,KAAAC;AASO,IAAM,6BAAN,eAAyCA,MAAA,cAC5BD,MAAAD,SAD4BE,KAAa;AAAA,EAM3D,YAAY;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb;AAAA,EACF,IAII,CAAC,GAAG;AACN,UAAM,EAAE,SAAS,YAAY,MAAM,CAAC;AAdtC,SAAkBD,OAAU;AAE5B;AAAA,SAAS,OAAOH;AAChB,SAAS,OAAO;AAAA,EAYhB;AAAA,EAEA,OAAO,WAAW,OAAqD;AACrE,WAAO,aAAa,UAAU,KAAK,KAAKE,WAAU;AAAA,EACpD;AACF;;;AC3BA,IAAMG,QAAO;AACb,IAAMC,UAAS,2BAA2BD,KAAI;AAC9C,IAAME,UAAS,OAAO,IAAID,OAAM;AALhC,IAAAE,KAAAC;AAUO,IAAM,uBAAN,eAAmCA,MAAA,cACtBD,MAAAD,SADsBE,KAAa;AAAA,EAQrD,YAAY;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAMI,CAAC,GAAG;AACN,UAAM,EAAE,SAAS,YAAY,MAAM,CAAC;AApBtC,SAAkBD,OAAU;AAE5B;AAAA,SAAS,OAAOH;AAChB,SAAS,OAAO;AAkBd,SAAK,WAAW;AAChB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EAEA,OAAO,WAAW,OAA+C;AAC/D,WAAO,aAAa,UAAU,KAAK,KAAKE,WAAU;AAAA,EACpD;AACF;;;AP3BO,SAAS,+BAA+B;AAAA,EAC7C;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA;AACF,GAMiB;AACf,QAAM,cAAc,2BAA2B,UAAU,QAAQ;AACjE,MAAI,CAAC,YAAY,SAAS;AACxB,WAAO,IAAI,qBAAqB;AAAA,MAC9B,SAAS,kCAAkC,cAAc;AAAA,MACzD;AAAA,MACA;AAAA,MACA,iBAAiB,YAAY;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,oBAA0C,YAAY;AAC5D,QAAM,YAAY,kBAAkB,MAAM;AAC1C,QAAM,UAAU,kBAAkB,MAAM;AAExC,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,aAAO,2BAA2B,sBAAsB;AAAA,QACtD,gBAAgB,eAAe;AAAA,QAC/B,mBAAmB,eAAe;AAAA,QAClC;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,KAAK;AACH,aAAO,IAAI,2BAA2B,EAAE,SAAS,YAAY,MAAM,CAAC;AAAA,IACtE,KAAK;AACH,aAAO,IAAI,sBAAsB,EAAE,SAAS,YAAY,MAAM,CAAC;AAAA,IACjE,KAAK,mBAAmB;AACtB,YAAM,cAAc,yBAAyB;AAAA,QAC3C,kBAAkB,MAAM;AAAA,MAC1B;AACA,aAAO,IAAI,0BAA0B;AAAA,QACnC;AAAA,QACA;AAAA,QACA,SAAS,YAAY,UAAU,YAAY,KAAK,UAAU;AAAA,QAC1D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,KAAK;AACH,aAAO,IAAI,2BAA2B,EAAE,SAAS,YAAY,MAAM,CAAC;AAAA,IACtE;AACE,aAAO,IAAI,2BAA2B,EAAE,SAAS,YAAY,MAAM,CAAC;AAAA,EACxE;AACF;AAEA,IAAM,6BAA6B,aAAE,OAAO;AAAA,EAC1C,OAAO,aAAE,OAAO;AAAA,IACd,SAAS,aAAE,OAAO;AAAA,IAClB,MAAM,aAAE,OAAO,EAAE,QAAQ;AAAA,IACzB,OAAO,aAAE,QAAQ,EAAE,QAAQ;AAAA,IAC3B,MAAM,aAAE,MAAM,CAAC,aAAE,OAAO,GAAG,aAAE,OAAO,CAAC,CAAC,EAAE,QAAQ;AAAA,EAClD,CAAC;AACH,CAAC;;;ADzEM,SAAS,eACd,OACA,YACA;AAPF,MAAAG;AAQE,MAAI,aAAa,WAAW,KAAK,GAAG;AAClC,WAAO;AAAA,EACT;AAEA,MAAI,6BAAa,WAAW,KAAK,GAAG;AAClC,WAAO,+BAA+B;AAAA,MACpC,UAAU,uBAAuB,KAAK;AAAA,MACtC,aAAYA,MAAA,MAAM,eAAN,OAAAA,MAAoB;AAAA,MAChC,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,+BAA+B;AAAA,IACpC,UAAU,CAAC;AAAA,IACX,YAAY;AAAA,IACZ,gBACE,iBAAiB,QACb,2BAA2B,MAAM,OAAO,KACxC;AAAA,IACN,OAAO;AAAA,IACP;AAAA,EACF,CAAC;AACH;;;AS9BO,SAAS,uBAAuB,OAA8B;AACnE,MAAI,MAAM,SAAS,QAAW;AAC5B,WAAO,MAAM;AAAA,EACf;AACA,MAAI,MAAM,gBAAgB,MAAM;AAC9B,QAAI;AACF,aAAO,KAAK,MAAM,MAAM,YAAY;AAAA,IACtC,SAAQ;AACN,aAAO,MAAM;AAAA,IACf;AAAA,EACF;AACA,SAAO,CAAC;AACV;;;ACdA,IAAAC,aAAkB;AAEX,IAAM,6BAA6B;AAEnC,SAAS,gBAAgB,SAA6C;AAC3E,QAAM,SAAS,wBAAwB;AAAA,IACrC,QAAQ,0BAA0B;AAAA,EACpC;AACA,SAAO,OAAO,UAAU,OAAO,OAAO;AACxC;AAEA,IAAM,0BAA0B,aAAE,MAAM;AAAA,EACtC,aAAE,QAAQ,SAAS;AAAA,EACnB,aAAE,QAAQ,MAAM;AAClB,CAAC;;;ACdD,4BAKO;AAIP,IAAAC,aAAkB;AAQX,IAAM,uBAAN,MAA2B;AAAA,EAChC,YAA6B,QAAoC;AAApC;AAAA,EAAqC;AAAA,EAElE,MAAM,qBAA4D;AAChE,QAAI;AACF,YAAM,EAAE,MAAM,IAAI,UAAM,kCAAW;AAAA,QACjC,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,QAC3B,SAAS,UAAM,+BAAQ,KAAK,OAAO,QAAQ,CAAC;AAAA,QAC5C,+BAA2B;AAAA,UACzB;AAAA,QACF;AAAA,QACA,2BAAuB,sDAA+B;AAAA,UACpD,aAAa,aAAE,IAAI;AAAA,UACnB,gBAAgB,UAAQ;AAAA,QAC1B,CAAC;AAAA,QACD,OAAO,KAAK,OAAO;AAAA,MACrB,CAAC;AAED,aAAO;AAAA,IACT,SAAS,OAAO;AACd,YAAM,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AACF;AAEA,IAAM,0CAA0C,aAAE,OAAO;AAAA,EACvD,sBAAsB,aAAE,QAAQ,IAAI;AAAA,EACpC,UAAU,aAAE,OAAO;AAAA,EACnB,SAAS,aAAE,OAAO;AACpB,CAAC;AAED,IAAM,oCAAoC,aAAE,OAAO;AAAA,EACjD,OAAO,aAAE,OAAO;AAAA,EAChB,QAAQ,aAAE,OAAO;AACnB,CAAC;AAED,IAAM,kCAAkC,aAAE,OAAO;AAAA,EAC/C,IAAI,aAAE,OAAO;AAAA,EACb,MAAM,aAAE,OAAO;AAAA,EACf,aAAa,aAAE,OAAO,EAAE,QAAQ;AAAA,EAChC,SAAS,kCAAkC,QAAQ;AAAA,EACnD,eAAe;AACjB,CAAC;AAED,IAAM,6BAA6B,aAAE,OAAO;AAAA,EAC1C,QAAQ,aAAE,MAAM,+BAA+B;AACjD,CAAC;;;ACxDD,IAAAC,yBASO;AACP,IAAAC,aAAkB;AAWX,IAAM,uBAAN,MAAsD;AAAA,EAI3D,YACW,SACQ,QACjB;AAFS;AACQ;AALnB,SAAS,uBAAuB;AAChC,SAAS,gBAAgB,EAAE,OAAO,CAAC,IAAI,EAAE;AAAA,EAKtC;AAAA,EAEH,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,MAAc,QAAQ,SAAuD;AAC3E,UAAM,EAAE,aAAa,cAAc,GAAG,qBAAqB,IAAI;AAE/D,WAAO;AAAA,MACL,MAAM,KAAK,qBAAqB,oBAAoB;AAAA,MACpD,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EAEA,MAAM,WACJ,SAC6D;AAC7D,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ,OAAO;AACrD,UAAM,EAAE,YAAY,IAAI;AAExB,UAAM,kBAAkB,UAAM,gCAAQ,KAAK,OAAO,QAAQ,CAAC;AAE3D,QAAI;AACF,YAAM;AAAA,QACJ;AAAA,QACA,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,IAAI,UAAM,sCAAc;AAAA,QACtB,KAAK,KAAK,OAAO;AAAA,QACjB,aAAS;AAAA,UACP;AAAA,UACA,QAAQ;AAAA,UACR,KAAK,sBAAsB,KAAK,SAAS,KAAK;AAAA,UAC9C,UAAM,gCAAQ,KAAK,OAAO,WAAW;AAAA,QACvC;AAAA,QACA,MAAM;AAAA,QACN,+BAA2B,kDAA0B,aAAE,IAAI,CAAC;AAAA,QAC5D,2BAAuB,uDAA+B;AAAA,UACpD,aAAa,aAAE,IAAI;AAAA,UACnB,gBAAgB,UAAQ;AAAA,QAC1B,CAAC;AAAA,QACD,GAAI,eAAe,EAAE,YAAY;AAAA,QACjC,OAAO,KAAK,OAAO;AAAA,MACrB,CAAC;AAED,aAAO;AAAA,QACL,GAAG;AAAA,QACH,SAAS,EAAE,MAAM,KAAK;AAAA,QACtB,UAAU,EAAE,SAAS,iBAAiB,MAAM,YAAY;AAAA,QACxD;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,YAAM,eAAe,OAAO,gBAAgB,eAAe,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ,OAAO;AACrD,UAAM,EAAE,YAAY,IAAI;AAExB,UAAM,kBAAkB,UAAM,gCAAQ,KAAK,OAAO,QAAQ,CAAC;AAE3D,QAAI;AACF,YAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,UAAM,sCAAc;AAAA,QAC/D,KAAK,KAAK,OAAO;AAAA,QACjB,aAAS;AAAA,UACP;AAAA,UACA,QAAQ;AAAA,UACR,KAAK,sBAAsB,KAAK,SAAS,IAAI;AAAA,UAC7C,UAAM,gCAAQ,KAAK,OAAO,WAAW;AAAA,QACvC;AAAA,QACA,MAAM;AAAA,QACN,+BAA2B,yDAAiC,aAAE,IAAI,CAAC;AAAA,QACnE,2BAAuB,uDAA+B;AAAA,UACpD,aAAa,aAAE,IAAI;AAAA,UACnB,gBAAgB,UAAQ;AAAA,QAC1B,CAAC;AAAA,QACD,GAAI,eAAe,EAAE,YAAY;AAAA,QACjC,OAAO,KAAK,OAAO;AAAA,MACrB,CAAC;AAED,aAAO;AAAA,QACL,QAAQ,SAAS;AAAA,UACf,IAAI,gBAGF;AAAA,YACA,MAAM,YAAY;AAChB,kBAAI,SAAS,SAAS,GAAG;AACvB,2BAAW,QAAQ,EAAE,MAAM,gBAAgB,SAAS,CAAC;AAAA,cACvD;AAAA,YACF;AAAA,YACA,UAAU,OAAO,YAAY;AAC3B,kBAAI,MAAM,SAAS;AACjB,sBAAM,aAAa,MAAM;AAIzB,oBAAI,WAAW,SAAS,SAAS,CAAC,QAAQ,kBAAkB;AAC1D;AAAA,gBACF;AAEA,oBACE,WAAW,SAAS,uBACpB,WAAW,aACX,OAAO,WAAW,cAAc,UAChC;AACA,6BAAW,YAAY,IAAI,KAAK,WAAW,SAAS;AAAA,gBACtD;AAEA,2BAAW,QAAQ,UAAU;AAAA,cAC/B,OAAO;AACL,2BAAW;AAAA,kBACR,MAA6C;AAAA,gBAChD;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,SAAS,EAAE,MAAM,KAAK;AAAA,QACtB,UAAU,EAAE,SAAS,gBAAgB;AAAA,MACvC;AAAA,IACF,SAAS,OAAO;AACd,YAAM,eAAe,OAAO,gBAAgB,eAAe,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA,EAEQ,WAAW,MAAe;AAChC,WACE,QAAQ,OAAO,SAAS,YAAY,UAAU,QAAQ,KAAK,SAAS;AAAA,EAExE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQQ,qBAAqB,SAAqC;AAChE,eAAW,WAAW,QAAQ,QAAQ;AACpC,iBAAW,QAAQ,QAAQ,SAAS;AAClC,YAAI,KAAK,WAAW,IAAI,GAAG;AACzB,gBAAM,WAAW;AAIjB,cAAI,SAAS,gBAAgB,YAAY;AACvC,kBAAM,SAAS,WAAW,KAAK,SAAS,IAAI;AAC5C,kBAAM,aAAa,OAAO,KAAK,MAAM,EAAE,SAAS,QAAQ;AACxD,qBAAS,OAAO,IAAI;AAAA,cAClB,QAAQ,SAAS,aAAa,0BAA0B,WAAW,UAAU;AAAA,YAC/E;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,SAAS;AACf,WAAO,GAAG,KAAK,OAAO,OAAO;AAAA,EAC/B;AAAA,EAEQ,sBAAsB,SAAiB,WAAoB;AACjE,WAAO;AAAA,MACL,2CAA2C;AAAA,MAC3C,wBAAwB;AAAA,MACxB,+BAA+B,OAAO,SAAS;AAAA,IACjD;AAAA,EACF;AACF;;;AChNA,IAAAC,yBAOO;AACP,IAAAC,aAAkB;AAMX,IAAM,wBAAN,MAAgE;AAAA,EAKrE,YACW,SACQ,QAIjB;AALS;AACQ;AANnB,SAAS,uBAAuB;AAChC,SAAS,uBAAuB;AAChC,SAAS,wBAAwB;AAAA,EAQ9B;AAAA,EAEH,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,MAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAEE;AAvCJ,QAAAC;AAwCI,UAAM,kBAAkB,UAAM,gCAAQ,KAAK,OAAO,QAAQ,CAAC;AAC3D,QAAI;AACF,YAAM;AAAA,QACJ;AAAA,QACA,OAAO;AAAA,QACP;AAAA,MACF,IAAI,UAAM,sCAAc;AAAA,QACtB,KAAK,KAAK,OAAO;AAAA,QACjB,aAAS;AAAA,UACP;AAAA,UACA,4BAAW,CAAC;AAAA,UACZ,KAAK,sBAAsB;AAAA,UAC3B,UAAM,gCAAQ,KAAK,OAAO,WAAW;AAAA,QACvC;AAAA,QACA,MAAM;AAAA,UACJ,OAAO,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA,UACzC,GAAI,4CAAmB,CAAC;AAAA,QAC1B;AAAA,QACA,+BAA2B;AAAA,UACzB;AAAA,QACF;AAAA,QACA,2BAAuB,uDAA+B;AAAA,UACpD,aAAa,aAAE,IAAI;AAAA,UACnB,gBAAgB,UAAQ;AAAA,QAC1B,CAAC;AAAA,QACD,GAAI,eAAe,EAAE,YAAY;AAAA,QACjC,OAAO,KAAK,OAAO;AAAA,MACrB,CAAC;AAED,aAAO;AAAA,QACL,YAAY,aAAa;AAAA,QACzB,QAAOA,MAAA,aAAa,UAAb,OAAAA,MAAsB;AAAA,QAC7B,kBACE,aAAa;AAAA,QACf,UAAU,EAAE,SAAS,iBAAiB,MAAM,SAAS;AAAA,MACvD;AAAA,IACF,SAAS,OAAO;AACd,YAAM,eAAe,OAAO,gBAAgB,eAAe,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA,EAEQ,SAAS;AACf,WAAO,GAAG,KAAK,OAAO,OAAO;AAAA,EAC/B;AAAA,EAEQ,wBAAwB;AAC9B,WAAO;AAAA,MACL,4CAA4C;AAAA,MAC5C,eAAe,KAAK;AAAA,IACtB;AAAA,EACF;AACF;AAEA,IAAM,iCAAiC,aAAE,OAAO;AAAA,EAC9C,YAAY,aAAE,MAAM,aAAE,MAAM,aAAE,OAAO,CAAC,CAAC;AAAA,EACvC,OAAO,aAAE,OAAO,EAAE,QAAQ,aAAE,OAAO,EAAE,CAAC,EAAE,QAAQ;AAAA,EAChD,kBAAkB,aACf,OAAO,aAAE,OAAO,GAAG,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,QAAQ,CAAC,CAAC,EACpD,SAAS;AACd,CAAC;;;ACjGD,eAAsB,qBAAsC;AAF5D,MAAAC,KAAAC;AAGE,QAAM,SACJA,OAAAD,MAAA,WAAW,EAAE,YAAb,gBAAAA,IAAuB,2BAAvB,OAAAC,MACA,QAAQ,IAAI;AAEd,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,2BAA2B;AAAA,MACnC,SAAS;AAAA,MACT,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,eAAsB,qBAAkD;AAjBxE,MAAAD;AAkBE,UAAOA,MAAA,WAAW,EAAE,YAAb,gBAAAA,IAAuB;AAChC;AAMA,IAAM,yBAAyB,OAAO,IAAI,yBAAyB;AAEnE,SAAS,aAAsB;AA3B/B,MAAAA,KAAAC,KAAA;AA4BE,QAAM,aAEF;AACJ,UAAO,MAAAA,OAAAD,MAAA,WAAW,sBAAsB,MAAjC,gBAAAA,IAAoC,QAApC,gBAAAC,IAAA,KAAAD,SAAA,YAA+C,CAAC;AACzD;;;AfkDA,IAAM,8BAA8B;AAK7B,SAAS,sBACd,UAAmC,CAAC,GACnB;AAzFnB,MAAAE,KAAAC;AA0FE,MAAI,kBAAgE;AACpE,MAAI,gBAAqD;AACzD,QAAM,sBACJD,MAAA,QAAQ,+BAAR,OAAAA,MAAsC,MAAO,KAAK;AACpD,MAAI,gBAAgB;AAEpB,QAAM,WACJC,UAAA,6CAAqB,QAAQ,OAAO,MAApC,OAAAA,MACA;AAEF,QAAM,aAAa,YAAY;AAC7B,UAAM,OAAO,MAAM,oBAAoB,OAAO;AAC9C,QAAI,MAAM;AACR,aAAO;AAAA,QACL,eAAe,UAAU,KAAK,KAAK;AAAA,QACnC,+BAA+B;AAAA,QAC/B,CAAC,0BAA0B,GAAG,KAAK;AAAA,QACnC,GAAG,QAAQ;AAAA,MACb;AAAA,IACF;AAEA,UAAM,2BAA2B,sBAAsB;AAAA,MACrD,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAEA,QAAM,oBAAoB,MAAM;AAC9B,UAAM,mBAAe,4CAAoB;AAAA,MACvC,cAAc;AAAA,MACd,yBAAyB;AAAA,IAC3B,CAAC;AACD,UAAM,kBAAc,4CAAoB;AAAA,MACtC,cAAc;AAAA,MACd,yBAAyB;AAAA,IAC3B,CAAC;AACD,UAAM,aAAS,4CAAoB;AAAA,MACjC,cAAc;AAAA,MACd,yBAAyB;AAAA,IAC3B,CAAC;AAED,WAAO,YAAY;AACjB,YAAM,YAAY,MAAM,mBAAmB;AAC3C,aAAO;AAAA,QACL,GAAI,gBAAgB,EAAE,yBAAyB,aAAa;AAAA,QAC5D,GAAI,eAAe,EAAE,uBAAuB,YAAY;AAAA,QACxD,GAAI,UAAU,EAAE,kBAAkB,OAAO;AAAA,QACzC,GAAI,aAAa,EAAE,sBAAsB,UAAU;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,sBAAsB,CAAC,YAA4B;AACvD,WAAO,IAAI,qBAAqB,SAAS;AAAA,MACvC,UAAU;AAAA,MACV;AAAA,MACA,SAAS;AAAA,MACT,OAAO,QAAQ;AAAA,MACf,aAAa,kBAAkB;AAAA,IACjC,CAAC;AAAA,EACH;AAEA,QAAM,qBAAqB,YAAY;AAzJzC,QAAAD,KAAAC,KAAA;AA0JI,UAAM,OAAM,MAAAA,OAAAD,MAAA,QAAQ,cAAR,gBAAAA,IAAmB,gBAAnB,gBAAAC,IAAA,KAAAD,KAAmC,cAAnC,YAAgD,KAAK,IAAI;AACrE,QAAI,CAAC,mBAAmB,MAAM,gBAAgB,oBAAoB;AAChE,sBAAgB;AAEhB,wBAAkB,IAAI,qBAAqB;AAAA,QACzC;AAAA,QACA,SAAS;AAAA,QACT,OAAO,QAAQ;AAAA,MACjB,CAAC,EACE,mBAAmB,EACnB,KAAK,cAAY;AAChB,wBAAgB;AAChB,eAAO;AAAA,MACT,CAAC,EACA,MAAM,OAAO,UAAmB;AAC/B,cAAM,eAAe,OAAO,gBAAgB,MAAM,WAAW,CAAC,CAAC;AAAA,MACjE,CAAC;AAAA,IACL;AAEA,WAAO,gBAAgB,QAAQ,QAAQ,aAAa,IAAI;AAAA,EAC1D;AAEA,QAAM,WAAW,SAAU,SAAyB;AAClD,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,oBAAoB,OAAO;AAAA,EACpC;AAEA,WAAS,qBAAqB;AAC9B,WAAS,aAAa,CAAC,YAAoB;AACzC,UAAM,IAAI,kCAAiB,EAAE,SAAS,WAAW,aAAa,CAAC;AAAA,EACjE;AACA,WAAS,gBAAgB;AACzB,WAAS,qBAAqB,CAAC,YAAqC;AAClE,WAAO,IAAI,sBAAsB,SAAS;AAAA,MACxC,UAAU;AAAA,MACV;AAAA,MACA,SAAS;AAAA,MACT,OAAO,QAAQ;AAAA,MACf,aAAa,kBAAkB;AAAA,IACjC,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEO,IAAM,UAAU,sBAAsB;AAE7C,eAAsB,oBACpB,SAIQ;AACR,QAAM,aAAS,4CAAoB;AAAA,IACjC,cAAc,QAAQ;AAAA,IACtB,yBAAyB;AAAA,EAC3B,CAAC;AAED,MAAI,QAAQ;AACV,WAAO;AAAA,MACL,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,EACF;AAEA,MAAI;AACF,UAAM,YAAY,MAAM,mBAAmB;AAC3C,WAAO;AAAA,MACL,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,EACF,SAAQ;AACN,WAAO;AAAA,EACT;AACF;", "names": ["import_provider", "import_provider_utils", "import_v4", "marker", "symbol", "_a", "_b", "name", "marker", "symbol", "_a", "_b", "name", "marker", "symbol", "_a", "_b", "name", "marker", "symbol", "_a", "_b", "name", "marker", "symbol", "_a", "_b", "name", "marker", "symbol", "_a", "_b", "_a", "import_v4", "import_v4", "import_provider_utils", "import_v4", "import_provider_utils", "import_v4", "_a", "_a", "_b", "_a", "_b"]}